using Application.Features.Generic.Specifications;
using Application.Features.Sample;
using Domain.Entities;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Ukázkové použití EntityFacade pro jednoduché operace s entitami
/// </summary>
public class EntityFacadeUsageExamples
{
    private readonly IEntityFacade _entityFacade;

    public EntityFacadeUsageExamples(IEntityFacade entityFacade)
    {
        _entityFacade = entityFacade;
    }

    /// <summary>
    /// Ukázka základních CRUD operací s minimálními generickými parametry
    /// </summary>
    public async Task BasicCrudExampleAsync()
    {
        // === VYTVOŘENÍ ENTITY ===
        // Pouze jeden generický parametr TEntity!
        var createDto = new SampleAddEdit
        {
            Name = "Nová ukázka",
            Description = "Popis ukázky",
            IsActive = true
        };

        var createResult = await _entityFacade.CreateAsync<SampleEntity>(createDto);

        createResult
            .OnSuccess(id => Console.WriteLine($"Entita vytvořena s ID: {id}"))
            .OnError(errors => Console.WriteLine($"Chyba při vytváření entity: {string.Join(", ", errors)}"));

        if (createResult.Succeeded)
        {
            var entityId = createResult.GetValueOrThrow();

            // === NAČTENÍ ENTITY ===
            var getResult = await _entityFacade.GetByIdAsync<SampleEntity>(entityId, useCache: true);

            getResult
                .OnSuccess(data => Console.WriteLine($"Entita načtena: {data}"))
                .OnError(errors => Console.WriteLine($"Chyba při načítání entity: {string.Join(", ", errors)}"));

            if (getResult.Succeeded && getResult.Data != null)
            {
                // === AKTUALIZACE ENTITY ===
                var updateDto = new SampleAddEdit
                {
                    Name = "Aktualizovaná ukázka",
                    Description = "Aktualizovaný popis",
                    IsActive = true
                };

                var updateResult = await _entityFacade.UpdateAsync<SampleEntity>(entityId, updateDto);

                updateResult
                    .OnSuccess(_ => Console.WriteLine("Entita úspěšně aktualizována"))
                    .OnError(errors => Console.WriteLine($"Chyba při aktualizaci entity: {string.Join(", ", errors)}"));

                if (updateResult.Succeeded)
                {
                    // === SMAZÁNÍ ENTITY ===
                    var deleteResult = await _entityFacade.DeleteAsync<SampleEntity>(entityId);

                    deleteResult
                        .OnSuccess(_ => Console.WriteLine("Entita úspěšně smazána"))
                        .OnError(errors => Console.WriteLine($"Chyba při mazání entity: {string.Join(", ", errors)}"));
                }
            }
        }
    }

    /// <summary>
    /// Ukázka dotazů s filtrováním pomocí specifikací
    /// </summary>
    public async Task QueryWithSpecificationExampleAsync()
    {
        // === ZÍSKÁNÍ VŠECH AKTIVNÍCH ENTIT ===
        var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
        var allActiveResult = await _entityFacade.GetAllAsync<SampleEntity>(
            specification: activeSpec,
            useCache: true);

        allActiveResult
            .OnSuccess(data => Console.WriteLine($"Nalezeno {data?.Count() ?? 0} aktivních entit"))
            .OnError(errors => Console.WriteLine($"Chyba při načítání aktivních entit: {string.Join(", ", errors)}"));

        // === VYHLEDÁVÁNÍ PODLE NÁZVU ===
        var searchSpec = new SampleSpecifications.SamplesByNameSpecification("test");
        var searchResult = await _entityFacade.GetAllAsync<SampleEntity>(
            specification: searchSpec,
            useCache: false);

        searchResult
            .OnSuccess(data => Console.WriteLine($"Nalezeno {data?.Count() ?? 0} entit obsahujících 'test'"))
            .OnError(errors => Console.WriteLine($"Chyba při vyhledávání entit: {string.Join(", ", errors)}"));

        // === STRÁNKOVANÉ VÝSLEDKY ===
        var pagedResult = await _entityFacade.GetPagedAsync<SampleEntity>(
            pageNumber: 1,
            pageSize: 10,
            specification: activeSpec,
            useCache: true);

        if (pagedResult.Succeeded)
        {
            Console.WriteLine($"Stránka 1: {pagedResult.Items.Count()} z {pagedResult.TotalCount} entit");
        }
        else
        {
            Console.WriteLine($"Chyba při načítání stránkovaných výsledků: {string.Join(", ", pagedResult.Errors)}");
        }
    }

    /// <summary>
    /// Ukázka silně typovaných dotazů pro pokročilé použití
    /// </summary>
    public async Task StronglyTypedQueryExampleAsync()
    {
        // Pro pokročilé použití můžeme stále specifikovat TDto
        var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
        
        var typedResult = await _entityFacade.GetAllAsync<SampleEntity, SampleDto>(
            specification: activeSpec,
            useCache: true);

        typedResult
            .OnSuccess(data =>
            {
                // Zde máme silně typovaný List<SampleDto>
                foreach (var sample in data)
                {
                    Console.WriteLine($"Sample: {sample.Name} - {sample.Description}");
                }
            })
            .OnError(errors => Console.WriteLine($"Chyba při načítání typovaných dat: {string.Join(", ", errors)}"));

        var typedPagedResult = await _entityFacade.GetPagedAsync<SampleEntity, SampleDto>(
            pageNumber: 1,
            pageSize: 5,
            specification: activeSpec);

        if (typedPagedResult.Succeeded)
        {
            // Zde máme silně typovaný PagedResult<SampleDto>
            Console.WriteLine($"Stránkované výsledky: {typedPagedResult.Items.Count} z {typedPagedResult.TotalCount}");
        }
        else
        {
            Console.WriteLine($"Chyba při načítání stránkovaných typovaných dat: {string.Join(", ", typedPagedResult.Errors)}");
        }
    }

    /// <summary>
    /// Ukázka práce s registry typů
    /// </summary>
    public async Task TypeRegistryExampleAsync()
    {
        // Kontrola, zda je entita registrována
        if (_entityFacade.IsEntityRegistered<SampleEntity>())
        {
            Console.WriteLine("SampleEntity je registrována v registry");

            // Získání informací o typech
            var typeInfo = _entityFacade.GetEntityTypeInfo<SampleEntity>();
            if (typeInfo != null)
            {
                Console.WriteLine($"Entity Type: {typeInfo.EntityType.Name}");
                Console.WriteLine($"DTO Type: {typeInfo.DtoType.Name}");
                Console.WriteLine($"Edit DTO Type: {typeInfo.EditDtoType.Name}");
                Console.WriteLine($"Key Type: {typeInfo.KeyType.Name}");
            }
        }
        else
        {
            Console.WriteLine("SampleEntity není registrována - nelze použít fasádu");
        }
    }

    /// <summary>
    /// Ukázka kombinace operací v jednom workflow
    /// </summary>
    public async Task CompleteWorkflowExampleAsync()
    {
        Console.WriteLine("=== Kompletní workflow s EntityFacade ===");

        try
        {
            // 1. Vytvoření několika entit
            var entities = new[]
            {
                new SampleAddEdit { Name = "Entita 1", Description = "První entita", IsActive = true },
                new SampleAddEdit { Name = "Entita 2", Description = "Druhá entita", IsActive = false },
                new SampleAddEdit { Name = "Test entita", Description = "Testovací entita", IsActive = true }
            };

            var createdIds = new List<object>();
            foreach (var entity in entities)
            {
                var result = await _entityFacade.CreateAsync<SampleEntity>(entity);

                result
                    .OnSuccess(id =>
                    {
                        createdIds.Add(id);
                        Console.WriteLine($"Vytvořena entita s ID: {id}");
                    })
                    .OnError(errors => Console.WriteLine($"Chyba při vytváření entity: {string.Join(", ", errors)}"));
            }

            // 2. Načtení aktivních entit
            var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
            var activeEntities = await _entityFacade.GetAllAsync<SampleEntity>(activeSpec, useCache: true);
            
            if (activeEntities.Succeeded)
            {
                Console.WriteLine($"Nalezeno {activeEntities.Data.Count()} aktivních entit");
            }

            // 3. Vyhledání entit obsahujících "Test"
            var searchSpec = new SampleSpecifications.SamplesByNameSpecification("Test");
            var searchResults = await _entityFacade.GetAllAsync<SampleEntity>(searchSpec);
            
            if (searchResults.Succeeded)
            {
                Console.WriteLine($"Nalezeno {searchResults.Data.Count()} entit obsahujících 'Test'");
            }

            // 4. Stránkované načtení
            var pagedResults = await _entityFacade.GetPagedAsync<SampleEntity>(1, 2, activeSpec);
            if (pagedResults.Succeeded)
            {
                Console.WriteLine($"Stránka 1 (velikost 2): {pagedResults.Items.Count()} z {pagedResults.TotalCount} entit");
            }

            // 5. Úklid - smazání vytvořených entit
            foreach (var id in createdIds)
            {
                var deleteResult = await _entityFacade.DeleteAsync<SampleEntity>(id);

                deleteResult
                    .OnSuccess(_ => Console.WriteLine($"Smazána entita s ID: {id}"))
                    .OnError(errors => Console.WriteLine($"Chyba při mazání entity {id}: {string.Join(", ", errors)}"));
            }

            Console.WriteLine("=== Workflow dokončen ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Chyba ve workflow: {ex.Message}");
        }
    }
}

/// <summary>
/// Porovnání starého a nového přístupu
/// </summary>
public class FacadeComparisonExample
{
    /// <summary>
    /// STARÝ PŘÍSTUP - mnoho generických parametrů
    /// </summary>
    public void OldApproach()
    {
        // Musíme specifikovat všechny typy
        // var command = new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { ... };
        // var query = new GetAllEntitiesQuery<SampleDto, SampleEntity> { ... };
        // var pagedQuery = new GetPagedEntitiesQuery<SampleDto, SampleEntity> { ... };
    }

    /// <summary>
    /// NOVÝ PŘÍSTUP - pouze jeden generický parametr
    /// </summary>
    public async Task NewApproach(IEntityFacade facade)
    {
        // Pouze TEntity - ostatní typy se odvodí automaticky!
        var createResult = await facade.CreateAsync<SampleEntity>(new SampleAddEdit());
        var allResults = await facade.GetAllAsync<SampleEntity>();
        var pagedResults = await facade.GetPagedAsync<SampleEntity>(1, 10);
        
        // Pro pokročilé použití stále můžeme specifikovat TDto
        var typedResults = await facade.GetAllAsync<SampleEntity, SampleDto>();
    }
}
