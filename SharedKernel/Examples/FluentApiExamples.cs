using System;
using System.Threading.Tasks;
using SharedKernel.Models;

namespace SharedKernel.Examples;

/// <summary>
/// Příklady použití fluent API pro Result<T>.
/// <PERSON><PERSON><PERSON><PERSON>, jak nové metody zjednodu<PERSON><PERSON><PERSON><PERSON> bě<PERSON> v<PERSON>y práce s Result.
/// </summary>
public static class FluentApiExamples
{
    #region Před a po - Mapování dat

    /// <summary>
    /// PŘED: <PERSON>uč<PERSON><PERSON> mapování s kontrolou úspěchu
    /// </summary>
    public static async Task<Result<string>> GetUserDisplayName_Before(int userId, Func<int, Task<Result<User>>> getUser)
    {
        var userResult = await getUser(userId);

        if (!userResult.Succeeded)
            return Result<string>.Error(userResult.Errors);

        if (userResult.Data == null)
            return Result<string>.Error("User not found");

        var displayName = $"{userResult.Data.FirstName} {userResult.Data.LastName}";
        return Result<string>.Ok(displayName);
    }

    /// <summary>
    /// PO: Použití fluent API s Map
    /// </summary>
    public static async Task<Result<string>> GetUserDisplayName_After(int userId, Func<int, Task<Result<User>>> getUser)
    {
        var result = await getUser(userId);

        return result
            .Map(user => $"{user.FirstName} {user.LastName}")
            .OnSuccess(name => Console.WriteLine($"Generated display name: {name}"))
            .OnError(errors => Console.WriteLine($"Failed to get user: {string.Join(", ", errors)}"));
    }

    #endregion

    #region Před a po - Řetězení operací

    /// <summary>
    /// PŘED: Ruční řetězení s kontrolami
    /// </summary>
    public static async Task<Result<string>> ProcessUser_Before(
        int userId, 
        Func<int, Task<Result<User>>> getUser,
        Func<User, Task<Result<UserProfile>>> getProfile,
        Func<UserProfile, string> formatProfile)
    {
        var userResult = await getUser(userId);
        if (!userResult.Succeeded)
            return Result<string>.Error(userResult.Errors);
            
        if (userResult.Data == null)
            return Result<string>.Error("User not found");

        var profileResult = await getProfile(userResult.Data);
        if (!profileResult.Succeeded)
            return Result<string>.Error(profileResult.Errors);
            
        if (profileResult.Data == null)
            return Result<string>.Error("Profile not found");

        try
        {
            var formatted = formatProfile(profileResult.Data);
            return Result<string>.Ok(formatted);
        }
        catch (Exception ex)
        {
            return Result<string>.Error($"Formatting failed: {ex.Message}");
        }
    }

    /// <summary>
    /// PO: Použití fluent API s Bind
    /// </summary>
    public static async Task<Result<string>> ProcessUser_After(
        int userId,
        Func<int, Task<Result<User>>> getUser,
        Func<User, Task<Result<UserProfile>>> getProfile,
        Func<UserProfile, string> formatProfile)
    {
        var userResult = await getUser(userId);

        // Poznámka: Bind s async funkcemi vyžaduje jiný přístup
        if (!userResult.Succeeded)
            return Result<string>.Error(userResult.Errors);

        if (userResult.Data == null)
            return Result<string>.Error("User not found");

        var profileResult = await getProfile(userResult.Data);
        return profileResult.Map(profile => formatProfile(profile));
    }

    #endregion

    #region Před a po - Validace

    /// <summary>
    /// PŘED: Ruční validace
    /// </summary>
    public static Result<User> CreateUser_Before(string firstName, string lastName, string email)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            return Result<User>.Error("First name is required");
            
        if (string.IsNullOrWhiteSpace(lastName))
            return Result<User>.Error("Last name is required");
            
        if (string.IsNullOrWhiteSpace(email) || !email.Contains("@"))
            return Result<User>.Error("Valid email is required");

        var user = new User { FirstName = firstName, LastName = lastName, Email = email };
        return Result<User>.Ok(user);
    }

    /// <summary>
    /// PO: Použití fluent API s Create a validacemi
    /// </summary>
    public static Result<User> CreateUser_After(string firstName, string lastName, string email)
    {
        var user = new User { FirstName = firstName, LastName = lastName, Email = email };
        
        return Result<User>.Create(user,
            (u => !string.IsNullOrWhiteSpace(u?.FirstName), "First name is required"),
            (u => !string.IsNullOrWhiteSpace(u?.LastName), "Last name is required"),
            (u => !string.IsNullOrWhiteSpace(u?.Email) && u.Email.Contains("@"), "Valid email is required")
        );
    }

    #endregion

    #region Před a po - API Service pattern

    /// <summary>
    /// PŘED: Typický vzor v CrudApiService
    /// </summary>
    public static async Task<Result<T>> GetEntity_Before<T>(object id, Func<object, Task<Result<T>>> getEntity)
    {
        if (id == null)
            return Result<T>.Error("ID nesmí být null");

        var result = await getEntity(id);

        if (!result.Succeeded)
            return Result<T>.Error(result.Errors);

        if (result.Data == null)
            return Result<T>.Error("Entity not found");

        return Result<T>.Ok(result.Data);
    }

    /// <summary>
    /// PO: Zjednodušeno pomocí fluent API
    /// </summary>
    public static async Task<Result<T>> GetEntity_After<T>(object id, Func<object, Task<Result<T>>> getEntity)
    {
        if (id == null)
            return Result<T>.Error("ID nesmí být null");

        var result = await getEntity(id);

        return result
            .OnError(errors => Console.WriteLine($"Entity retrieval failed: {string.Join(", ", errors)}"))
            .Map(data => data ?? throw new InvalidOperationException("Data is null"));
    }

    #endregion

    #region Implicit conversion a convenience methods

    /// <summary>
    /// Ukázka implicit conversion
    /// </summary>
    public static Result<string> GetMessage()
    {
        // Implicit conversion z string na Result<string>
        return "Hello World!";
    }

    /// <summary>
    /// Ukázka GetValueOrThrow a GetValueOrDefault
    /// </summary>
    public static void ConvenienceMethodsExample()
    {
        var successResult = Result<string>.Ok("Success!");
        var errorResult = Result<string>.Error("Something went wrong");

        // GetValueOrDefault
        var value1 = successResult.GetValueOrDefault("Default"); // "Success!"
        var value2 = errorResult.GetValueOrDefault("Default");   // "Default"

        // GetValueOrThrow
        try
        {
            var value3 = successResult.GetValueOrThrow(); // "Success!"
            var value4 = errorResult.GetValueOrThrow();   // Vyhodí InvalidOperationException
        }
        catch (InvalidOperationException ex)
        {
            Console.WriteLine($"Expected exception: {ex.Message}");
        }
    }

    #endregion
}

/// <summary>
/// Ukázkové třídy pro příklady
/// </summary>
public class User
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class UserProfile
{
    public string Bio { get; set; } = string.Empty;
    public DateTime LastLogin { get; set; }
}
