using System;
using System.Linq;
using System.Threading.Tasks;

namespace SharedKernel.Models;

/// <summary>
/// Základní Result pattern pro reprezentaci výsledku operace.
/// Poskytuje společné vlastnosti pro všechny typy výsledků.
/// </summary>
public abstract record Result
{
    /// <summary>
    /// Indikuje, zda operace proběhla úspěšně.
    /// </summary>
    public bool Succeeded { get; protected init; }

    /// <summary>
    /// Seznam chybových zpráv v případě neúspěchu.
    /// </summary>
    public string[] Errors { get; protected init; } = Array.Empty<string>();

    /// <summary>
    /// Konstruktor pro odvozené třídy.
    /// </summary>
    /// <param name="succeeded">Indikuje úspěch operace</param>
    /// <param name="errors"><PERSON>ybo<PERSON><PERSON> zpr<PERSON>vy</param>
    protected Result(bool succeeded, string[] errors)
    {
        Succeeded = succeeded;
        Errors = errors;
    }
}

/// <summary>
/// Obecný Result pattern pro reprezentaci výsledku operace s daty.
/// Umožňuje jednotný způsob vracení úspěšných i neúspěšných výsledků.
/// </summary>
/// <typeparam name="T">Typ dat vracených při úspěšném výsledku</typeparam>
public record Result<T> : Result
{
    /// <summary>
    /// Data vrácená při úspěšném výsledku.
    /// </summary>
    public T? Data { get; }

    protected Result(bool succeeded, T? data, string[] errors) : base(succeeded, errors)
    {
        Data = data;
    }

    /// <summary>
    /// Vytvoří úspěšný výsledek s daty.
    /// </summary>
    /// <param name="data">Data k vrácení</param>
    /// <returns>Úspěšný Result s daty</returns>
    public static Result<T> Ok(T? data)
        => new Result<T>(true, data, Array.Empty<string>());

    /// <summary>
    /// Vytvoří neúspěšný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Neúspěšný Result s chybami</returns>
    public static Result<T> Error(params string[] errors)
        => new Result<T>(false, default, errors);

    /// <summary>
    /// Asynchronně vytvoří úspěšný výsledek s daty.
    /// </summary>
    /// <param name="data">Data k vrácení</param>
    /// <returns>Task s úspěšným Result</returns>
    public static Task<Result<T>> OkAsync(T? data)
        => Task.FromResult(Ok(data));

    /// <summary>
    /// Asynchronně vytvoří neúspěšný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Task s neúspěšným Result</returns>
    public static Task<Result<T>> ErrorAsync(params string[] errors)
        => Task.FromResult(Error(errors));

    #region Fluent API

    /// <summary>
    /// Mapuje úspěšný result na jiný typ.
    /// </summary>
    /// <typeparam name="TResult">Cílový typ</typeparam>
    /// <param name="mapper">Funkce pro mapování</param>
    /// <returns>Result s mapovanými daty nebo původní chyby</returns>
    public Result<TResult> Map<TResult>(Func<T, TResult> mapper)
    {
        if (!Succeeded)
            return Result<TResult>.Error(Errors);

        try
        {
            var mappedData = Data != null ? mapper(Data) : default;
            return Result<TResult>.Ok(mappedData);
        }
        catch (Exception ex)
        {
            return Result<TResult>.Error($"Mapping failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Kombinuje dva results pomocí binderu.
    /// </summary>
    /// <typeparam name="TResult">Cílový typ</typeparam>
    /// <param name="binder">Funkce pro kombinování</param>
    /// <returns>Result z binderu nebo původní chyby</returns>
    public Result<TResult> Bind<TResult>(Func<T, Result<TResult>> binder)
    {
        if (!Succeeded)
            return Result<TResult>.Error(Errors);

        try
        {
            return Data != null ? binder(Data) : Result<TResult>.Error("Data is null");
        }
        catch (Exception ex)
        {
            return Result<TResult>.Error($"Binding failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Provede akci pokud je result úspěšný.
    /// </summary>
    /// <param name="action">Akce k provedení</param>
    /// <returns>Původní Result pro řetězení</returns>
    public Result<T> OnSuccess(Action<T> action)
    {
        if (Succeeded && Data != null)
        {
            try
            {
                action(Data);
            }
            catch (Exception ex)
            {
                return Error(Errors.Append($"OnSuccess action failed: {ex.Message}").ToArray());
            }
        }
        return this;
    }

    /// <summary>
    /// Provede akci pokud result není úspěšný.
    /// </summary>
    /// <param name="action">Akce k provedení s chybami</param>
    /// <returns>Původní Result pro řetězení</returns>
    public Result<T> OnError(Action<string[]> action)
    {
        if (!Succeeded)
        {
            try
            {
                action(Errors);
            }
            catch
            {
                // Ignorujeme chyby v error handleru
            }
        }
        return this;
    }

    #endregion

    #region Factory Methods with Validation

    /// <summary>
    /// Vytvoří result s validací hodnoty.
    /// </summary>
    /// <param name="data">Data k validaci</param>
    /// <param name="validator">Validační funkce</param>
    /// <param name="errorMessage">Chybová zpráva při neúspěšné validaci</param>
    /// <returns>Result s validovanými daty nebo chybou</returns>
    public static Result<T> Create(T? data, Func<T?, bool> validator, string errorMessage)
    {
        if (data == null)
            return Error("Data cannot be null");

        return validator(data)
            ? Ok(data)
            : Error(errorMessage);
    }

    /// <summary>
    /// Vytvoří result s více validacemi.
    /// </summary>
    /// <param name="data">Data k validaci</param>
    /// <param name="validations">Pole validací (validator, error message)</param>
    /// <returns>Result s validovanými daty nebo chybami</returns>
    public static Result<T> Create(T? data, params (Func<T?, bool> validator, string error)[] validations)
    {
        if (data == null)
            return Error("Data cannot be null");

        var errors = validations
            .Where(v => !v.validator(data))
            .Select(v => v.error)
            .ToArray();

        return errors.Any()
            ? Error(errors)
            : Ok(data);
    }

    #endregion

    #region Convenience Methods

    /// <summary>
    /// Implicit conversion z hodnoty na Result.
    /// </summary>
    /// <param name="data">Data k převodu</param>
    public static implicit operator Result<T>(T data) => Ok(data);

    /// <summary>
    /// Extrahuje hodnotu nebo vyhodí výjimku.
    /// </summary>
    /// <returns>Data z Result</returns>
    /// <exception cref="InvalidOperationException">Pokud Result není úspěšný</exception>
    public T GetValueOrThrow()
    {
        if (!Succeeded)
            throw new InvalidOperationException($"Result failed with errors: {string.Join(", ", Errors)}");

        return Data!;
    }

    /// <summary>
    /// Extrahuje hodnotu nebo vrátí default.
    /// </summary>
    /// <param name="defaultValue">Výchozí hodnota</param>
    /// <returns>Data z Result nebo výchozí hodnota</returns>
    public T? GetValueOrDefault(T? defaultValue = default)
    {
        return Succeeded ? Data : defaultValue;
    }

    #endregion
}
